#!/usr/bin/env python3
"""
Test script to verify ipfs-toolkit migration
"""

import os
import sys
import tempfile

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_ipfs_toolkit_import():
    """Test if ipfs-toolkit can be imported"""
    try:
        import ipfs_api
        print("✅ ipfs-toolkit (ipfs_api) imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import ipfs-toolkit: {e}")
        return False

def test_ipfs_toolkit_wrapper():
    """Test our IPFS toolkit wrapper"""
    try:
        from backend.ipfs_toolkit_wrapper import get_ipfs_client, IPFSToolkitClient
        print("✅ IPFS toolkit wrapper imported successfully")
        
        # Test client creation
        client = get_ipfs_client()
        print(f"✅ IPFS client created: {type(client)}")
        
        # Test connection status
        is_connected = client.is_connected()
        print(f"📡 IPFS connection status: {'Connected' if is_connected else 'Not connected'}")
        
        if is_connected:
            # Test basic operations
            try:
                # Test ID
                node_info = client.id()
                print(f"✅ IPFS node ID: {node_info.get('ID', 'unknown')}")
                
                # Test adding data
                test_data = "Hello, IPFS with ipfs-toolkit!"
                result = client.add(test_data)
                cid = result.get('Hash')
                print(f"✅ Added test data to IPFS: {cid}")
                
                # Test retrieving data
                retrieved_data = client.cat(cid)
                if retrieved_data.decode('utf-8') == test_data:
                    print("✅ Successfully retrieved and verified data from IPFS")
                else:
                    print("❌ Retrieved data doesn't match original")
                
                return True
                
            except Exception as e:
                print(f"⚠️ IPFS operations failed: {e}")
                return False
        else:
            print("⚠️ IPFS not connected, skipping operation tests")
            return True
            
    except Exception as e:
        print(f"❌ Failed to test IPFS toolkit wrapper: {e}")
        return False

def test_api_integration():
    """Test if the API can use the new IPFS client"""
    try:
        # Import the API module to check for errors
        from backend.api import ipfs_client, check_ipfs_connection
        print("✅ API module imported successfully with new IPFS client")
        
        # Test connection check
        is_connected = check_ipfs_connection()
        print(f"📡 API IPFS connection check: {'Connected' if is_connected else 'Not connected'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to test API integration: {e}")
        print(f"Error details: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing IPFS Toolkit Migration")
    print("=" * 50)
    
    tests = [
        ("IPFS Toolkit Import", test_ipfs_toolkit_import),
        ("IPFS Toolkit Wrapper", test_ipfs_toolkit_wrapper),
        ("API Integration", test_api_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 30)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! IPFS toolkit migration successful!")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
