"""
Verification and opening endpoints
"""
from fastapi import APIRout<PERSON>, Body, Depends
from typing import Dict, Any

from backend.models.verification import VerificationRequest, VerificationResponse, OpeningRequest, OpeningResponse
from backend.config import success_response, error_response, GROUP_MANAGER_ADDRESS, REVOCATION_MANAGER_ADDRESS
from backend.services.ipfs_service import IPFSService
from backend.groupsig_utils import open_signature_group_manager, open_signature_revocation_manager

router = APIRouter()

def get_ipfs_service() -> IPFSService:
    """Dependency to get IPFS service"""
    return IPFSService()

def clean_cid(cid: str) -> str:
    """Clean CID by removing quotes and whitespace"""
    if not cid:
        return cid
    return cid.strip().strip('"').strip("'")

@router.get("/verify/{cid}")
async def verify_content(
    cid: str,
    ipfs_service: IPFSService = Depends(get_ipfs_service)
):
    """
    Verify if content exists in IPFS or local storage
    
    Args:
        cid: The IPFS CID to verify
    """
    try:
        # Clean the CID
        clean_cid_value = clean_cid(cid)
        print(f"🔍 Verifying CID: {clean_cid_value}")

        # Use IPFS service to verify content
        result = ipfs_service.verify_content(clean_cid_value)
        
        print(f"✅ Verification complete: {result}")
        
        return success_response(data=result)
        
    except Exception as e:
        print(f"❌ Error verifying content: {str(e)}")
        error_response(str(e), 500)

@router.post("/opening/compute_partial")
async def compute_partial_opening(
    opening_id: int = Body(...),
    signature: str = Body(...),
    manager_type: str = Body(...),  # "group" or "revocation"
    wallet_address: str = Body(...)
):
    """
    Compute opening off-chain and approve on-chain (BBS04 only has one manager)
    """
    try:
        # Verify the caller is authorized using the predefined addresses
        if manager_type == "group" and wallet_address != GROUP_MANAGER_ADDRESS:
            error_response("Not authorized as Group Manager", 403)
        elif manager_type == "revocation" and wallet_address != REVOCATION_MANAGER_ADDRESS:
            error_response("Not authorized as Revocation Manager", 403)

        print(f"🔍 Computing partial opening {opening_id} by {manager_type} manager")

        # Perform the opening computation
        try:
            if manager_type == "group":
                # Use group manager opening
                opening_result = open_signature_group_manager(signature)
                print(f"✅ Group manager opening result: {opening_result}")
            else:
                # Use revocation manager opening
                opening_result = open_signature_revocation_manager(signature)
                print(f"✅ Revocation manager opening result: {opening_result}")

            if opening_result:
                return success_response(
                    data={
                        "opening_id": opening_id,
                        "success": True,
                        "result": str(opening_result),
                        "message": f"Opening computed successfully by {manager_type} manager"
                    }
                )
            else:
                return success_response(
                    data={
                        "opening_id": opening_id,
                        "success": False,
                        "result": None,
                        "message": f"Opening computation failed"
                    }
                )

        except Exception as opening_error:
            print(f"❌ Opening computation error: {str(opening_error)}")
            return success_response(
                data={
                    "opening_id": opening_id,
                    "success": False,
                    "result": None,
                    "message": f"Opening computation failed: {str(opening_error)}"
                }
            )

    except Exception as e:
        error_response(str(e), 500)

@router.post("/opening/request")
async def request_opening(
    signature: str = Body(...),
    reason: str = Body(...),
    wallet_address: str = Body(...)
):
    """
    Request signature opening (for authorized parties)
    """
    try:
        print(f"📝 Opening request from {wallet_address}: {reason}")

        # In a real implementation, this would:
        # 1. Validate the requester's authorization
        # 2. Store the opening request
        # 3. Notify the appropriate managers
        # 4. Return a request ID for tracking

        # For demo purposes, return a mock request ID
        import time
        request_id = int(time.time())

        return success_response(
            data={
                "request_id": request_id,
                "status": "pending",
                "signature": signature,
                "reason": reason,
                "requester": wallet_address,
                "message": "Opening request submitted successfully"
            }
        )

    except Exception as e:
        error_response(str(e), 500)

@router.get("/opening/status/{request_id}")
async def get_opening_status(request_id: int):
    """
    Get the status of an opening request
    """
    try:
        print(f"📊 Checking status of opening request {request_id}")

        # In a real implementation, this would query the database
        # For demo purposes, return a mock status
        return success_response(
            data={
                "request_id": request_id,
                "status": "pending",
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "result": None,
                "message": "Opening request is being processed"
            }
        )

    except Exception as e:
        error_response(str(e), 500)
