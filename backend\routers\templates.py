"""
Template management endpoints
"""
import os
import json
from fastapi import APIRouter, Depends
from typing import Dict, Any, Optional

from backend.config import success_response, error_response, LOCAL_STORAGE_PATH, BUYER_ADDRESS
from backend.services.ipfs_service import IPFSService

router = APIRouter()

def get_ipfs_service() -> IPFSService:
    """Dependency to get IPFS service"""
    return IPFSService()

def clean_cid(cid: str) -> str:
    """Clean CID by removing quotes and whitespace"""
    if not cid:
        return cid
    return cid.strip().strip('"').strip("'")

@router.get("/template/{template_cid}")
async def get_template(
    template_cid: str, 
    wallet_address: Optional[str] = None,
    ipfs_service: IPFSService = Depends(get_ipfs_service)
):
    """
    Retrieve template data from IPFS or local storage
    """
    try:
        # Clean the template CID
        clean_template_cid = clean_cid(template_cid)
        print(f"📖 Getting template data for CID: {clean_template_cid}")

        # Check if the wallet address is provided
        if wallet_address:
            print(f"🔍 Request from wallet address: {wallet_address}")

        # Try to retrieve the template data from IPFS
        template_data = ipfs_service.get_data(clean_template_cid)
        
        if template_data:
            try:
                # Parse JSON data
                template_json = json.loads(template_data.decode())
                print(f"✅ Retrieved template from IPFS: {len(template_data)} bytes")
                return success_response(data=template_json)
            except json.JSONDecodeError as e:
                print(f"❌ Error parsing template JSON: {str(e)}")
                error_response("Invalid template data format", 400)
        else:
            error_response("Template not found", 404)

    except Exception as e:
        print(f"❌ Error in get_template: {str(e)}")
        error_response(str(e), 500)

@router.post("/purchase/verify")
async def verify_purchase(
    request_id: str,
    wallet_address: str,
    template_cid: str,
    ipfs_service: IPFSService = Depends(get_ipfs_service)
):
    """
    Verify a purchase template
    """
    try:
        print(f"🔍 Verifying purchase template for request {request_id}")

        # Check if the wallet address is provided
        if wallet_address:
            print(f"🔍 Request from wallet address: {wallet_address}")

        # Get the template data
        clean_template_cid = clean_cid(template_cid)
        template_data = ipfs_service.get_data(clean_template_cid)
        
        if not template_data:
            error_response("Template not found", 404)

        try:
            # Parse the template data
            template_json = json.loads(template_data.decode())
            print(f"✅ Template verification successful")
            
            # Verify the template structure
            required_fields = ["patientId", "template_id", "filled_at", "request_id"]
            missing_fields = [field for field in required_fields if field not in template_json]
            
            if missing_fields:
                return success_response(
                    data={
                        "verified": False,
                        "template": template_json,
                        "missing_fields": missing_fields,
                        "message": f"Template missing required fields: {', '.join(missing_fields)}"
                    }
                )
            
            # Check if request_id matches
            if template_json.get("request_id") != request_id:
                return success_response(
                    data={
                        "verified": False,
                        "template": template_json,
                        "message": "Template request_id does not match"
                    }
                )
            
            return success_response(
                data={
                    "verified": True,
                    "template": template_json,
                    "message": "Template verification successful"
                }
            )
            
        except json.JSONDecodeError as e:
            print(f"❌ Error parsing template JSON: {str(e)}")
            error_response("Invalid template data format", 400)

    except Exception as e:
        print(f"❌ Error in verify_purchase: {str(e)}")
        error_response(str(e), 500)

@router.get("/templates/list")
async def list_templates(wallet_address: Optional[str] = None):
    """
    List available templates
    """
    try:
        print(f"📋 Listing templates for wallet: {wallet_address}")
        
        templates = []
        
        # Check purchase requests for templates
        purchases_dir = os.path.join(LOCAL_STORAGE_PATH, "purchases")
        if os.path.exists(purchases_dir):
            for filename in os.listdir(purchases_dir):
                if filename.endswith(".json"):
                    request_file = os.path.join(purchases_dir, filename)
                    try:
                        with open(request_file, "r") as f:
                            purchase_data = json.load(f)
                        
                        # Add template info if it exists
                        if "template_cid" in purchase_data:
                            template_info = {
                                "template_cid": purchase_data["template_cid"],
                                "request_id": purchase_data["request_id"],
                                "status": purchase_data.get("status", "unknown"),
                                "created_at": purchase_data.get("created_at"),
                                "buyer_address": purchase_data.get("buyer_address")
                            }
                            
                            # Add filled template info if available
                            if "filled_template_cid" in purchase_data:
                                template_info["filled_template_cid"] = purchase_data["filled_template_cid"]
                                template_info["patient_address"] = purchase_data.get("patient_address")
                            
                            templates.append(template_info)
                            
                    except Exception as e:
                        print(f"⚠️ Error loading request file {filename}: {str(e)}")
        
        # Sort by creation time (newest first)
        templates.sort(key=lambda x: x.get("created_at", 0), reverse=True)
        
        return success_response(data={"templates": templates})
        
    except Exception as e:
        print(f"❌ Error in list_templates: {str(e)}")
        error_response(str(e), 500)

@router.post("/templates/create")
async def create_template(
    template_data: Dict[str, Any],
    wallet_address: str,
    ipfs_service: IPFSService = Depends(get_ipfs_service)
):
    """
    Create a new template
    """
    try:
        print(f"📝 Creating template for wallet: {wallet_address}")
        
        # Add metadata to template
        template_with_metadata = {
            **template_data,
            "created_by": wallet_address,
            "created_at": time.time(),
            "template_version": "1.0"
        }
        
        # Store template on IPFS
        template_json = json.dumps(template_with_metadata).encode()
        template_cid = ipfs_service.add_data(template_json)
        
        print(f"✅ Template created with CID: {template_cid}")
        
        return success_response(
            data={
                "template_cid": template_cid,
                "template": template_with_metadata,
                "message": "Template created successfully"
            }
        )
        
    except Exception as e:
        print(f"❌ Error in create_template: {str(e)}")
        error_response(str(e), 500)
